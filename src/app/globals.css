@import "tailwindcss";

:root {
  --background: #f9fafb;
  --foreground: #111827;
  --primary: #2563eb;
  --accent: #f97316;
  --muted: #6b7280;
  --border: #e5e7eb;

  --surface: #ffffff; /* cards/nav base in light */
  --input-bg: #ffffff; /* input bg in light */
  --input-border: #e5e7eb; /* input border in light */
  --placeholder: #9ca3af; /* placeholder text */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --color-border: var(--border);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f1f5f9;
    --primary: #60a5fa;
    --accent: #fdba74;
    --muted: #94a3b8;
    --border: #1e293b;

    /* NEW (dark) */
    --surface: #111827; /* deep gray/nav surface */
    --input-bg: #0b1220; /* near-background, not pure black */
    --input-border: #1f2937; /* subtle dark border */
    --placeholder: #64748b;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

#lead {
  scroll-margin-top: 50px; /* Control the scroll offset */
}
