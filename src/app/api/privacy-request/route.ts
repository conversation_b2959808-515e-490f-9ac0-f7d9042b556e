import { NextResponse } from "next/server";

type PrivacyPayload = {
  email: string;
  zip?: string;
  requestType: "optout" | "access" | "delete";
  notes?: string;
};

export async function POST(req: Request) {
  // Accept both form posts and JSON
  let data: PrivacyPayload | null = null;
  const contentType = req.headers.get("content-type") || "";
  try {
    if (contentType.includes("application/json")) {
      data = (await req.json()) as PrivacyPayload;
    } else {
      const form = await req.formData();
      data = {
        email: String(form.get("email") || ""),
        zip: String(form.get("zip") || ""),
        requestType: String(
          form.get("requestType") || "optout"
        ) as PrivacyPayload["requestType"],
        notes: String(form.get("notes") || ""),
      };
    }
  } catch {}

  if (!data?.email) {
    return NextResponse.json(
      { ok: false, error: "email required" },
      { status: 400 }
    );
  }

  // Store or forward internally. We DO NOT send this to partners.
  // Option A: email to your privacy inbox via Resend/Postmark
  if (process.env.RESEND_API_KEY && process.env.LEAD_PRIVACY_EMAIL) {
    try {
      await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.RESEND_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: "<EMAIL>",
          to: process.env.LEAD_PRIVACY_EMAIL,
          subject: `[Privacy Request] ${data.requestType} — ${data.email}`,
          html: `<pre>${JSON.stringify(data, null, 2)}</pre>`,
        }),
      });
    } catch {}
  }

  // Option B: also log to a webhook/Sheet for tracking
  if (process.env.PRIVACY_WEBHOOK_URL) {
    try {
      await fetch(process.env.PRIVACY_WEBHOOK_URL, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...data, ts: new Date().toISOString() }),
      });
    } catch {}
  }

  return NextResponse.json({ ok: true });
}
