import { NextResponse } from "next/server";

// Server-side sanitization and validation
function sanitizeString(input: string): string {
  if (typeof input !== "string") return "";
  return input
    .trim()
    .replace(/[<>]/g, "") // Remove HTML tags
    .replace(/javascript:/gi, "") // Remove javascript protocol
    .replace(/on\w+=/gi, "") // Remove event handlers
    .replace(/[^\w\s@.-]/g, "") // Only allow alphanumeric, spaces, @, ., -
    .substring(0, 200); // Limit length
}

function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 100;
}

function validateZip(zip: string): boolean {
  const zipRegex = /^\d{5}(-\d{4})?$/;
  return zipRegex.test(zip);
}

export async function POST(req: Request) {
  const data = await req.json().catch(() => null);

  if (!data?.email || !data?.consent) {
    return NextResponse.json(
      { ok: false, error: "Missing required fields" },
      { status: 400 }
    );
  }

  // Sanitize all inputs
  const sanitizedData = {
    name: sanitizeString(data.name || ""),
    email: sanitizeString(data.email || ""),
    zip: sanitizeString(data.zip || ""),
    budget: sanitizeString(data.budget || ""),
    trim: sanitizeString(data.trim || "Any"),
    consent: Boolean(data.consent),
    doNotSell: Boolean(data.doNotSell),
  };

  // Validate sanitized data
  if (!validateEmail(sanitizedData.email)) {
    return NextResponse.json(
      { ok: false, error: "Invalid email format" },
      { status: 400 }
    );
  }

  if (sanitizedData.zip && !validateZip(sanitizedData.zip)) {
    return NextResponse.json(
      { ok: false, error: "Invalid ZIP code format" },
      { status: 400 }
    );
  }

  // Always: email internally so you have a record
  if (process.env.RESEND_API_KEY && process.env.LEAD_TO_EMAIL) {
    try {
      await fetch("https://api.resend.com/emails", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.RESEND_API_KEY}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          from: "<EMAIL>",
          to: process.env.LEAD_TO_EMAIL,
          subject: `New Raptor Finder lead${sanitizedData.doNotSell ? " (DO NOT SHARE)" : ""}`,
          html: `<pre>${JSON.stringify(sanitizedData, null, 2)}</pre>`,
        }),
      });
    } catch (error) {
      console.error("Email sending error:", error);
    }
  }

  // Only forward to your partner/dealer webhook if NOT opted out
  if (!sanitizedData.doNotSell && process.env.LEAD_WEBHOOK_URL) {
    try {
      await fetch(String(process.env.LEAD_WEBHOOK_URL), {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(sanitizedData),
      });
    } catch {}
  }

  return NextResponse.json({ ok: true });
}
