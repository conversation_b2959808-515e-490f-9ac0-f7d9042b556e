"use client";
import { useState } from "react";

type LeadPayload = {
  name: string;
  email: string;
  zip: string;
  budget?: string;
  trim?: string;
  consent: boolean;
  doNotSell?: boolean;
};

const listings = [
  {
    id: "cg-1",
    title: "2021 Ford F‑150 Raptor",
    price: "$64,900",
    miles: "28,300 mi",
    img: "/raptor-1.jpg", // put an image in /public
    href: "https://www.cargurus.com/Cars/inventorylisting/viewDetailsFilterViewInventoryListing.action?searchId=c79c406c-746b-4083-af25-cb53edb31929&zip=78723&distance=50000&entitySelectingHelper.selectedEntity=d337&sourceContext=untrackedWithinSite_false_0&sortDir=ASC&sortType=BEST_MATCH&makeModelTrimPaths=m2%2Fd337&makeModelTrimPaths=m2&makeModelTrimPaths=m2%2Fd337%2FRaptor&srpVariation=DEFAULT_SEARCH&isDeliveryEnabled=true&nonShippableBaseline=0", // your CJ affiliate link
    source: "CarGurus",
  },
  {
    id: "eb-2",
    title: "2018 Ford F‑150 Raptor",
    price: "$49,500",
    miles: "72,100 mi",
    img: "/raptor-2.jpg",
    href: "https://bringatrailer.com/ford/f-150-raptor/", // your EPN link
    source: "Bring A Trailer",
  },
];

export default function Page() {
  const [sending, setSending] = useState(false);
  const [status, setStatus] = useState<"idle" | "ok" | "err">("idle");

  // Input sanitization function
  const sanitizeInput = (input: string): string => {
    return input
      .trim()
      .replace(/[<>]/g, "") // Remove < and > to prevent HTML injection
      .replace(/javascript:/gi, "") // Remove javascript: protocol
      .replace(/on\w+=/gi, "") // Remove event handlers like onclick=
      .substring(0, 200); // Limit length
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validateZip = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  async function submitLead(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault();
    const f = new FormData(e.currentTarget);

    // Get and sanitize inputs
    const rawName = String(f.get("name") || "");
    const rawEmail = String(f.get("email") || "");
    const rawZip = String(f.get("zip") || "");
    const rawBudget = String(f.get("budget") || "");

    // Validate inputs
    if (!validateEmail(rawEmail)) {
      alert("Please enter a valid email address");
      return;
    }

    if (rawZip && !validateZip(rawZip)) {
      alert("Please enter a valid ZIP code (e.g., 12345 or 12345-6789)");
      return;
    }

    const payload: LeadPayload = {
      name: sanitizeInput(rawName),
      email: sanitizeInput(rawEmail),
      zip: sanitizeInput(rawZip),
      budget: sanitizeInput(rawBudget),
      trim: String(f.get("trim") || "Any"),
      consent: Boolean(f.get("consent")),
      doNotSell: Boolean(f.get("doNotSell")),
    };

    try {
      setSending(true);
      const res = await fetch("/api/lead", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      setStatus(res.ok ? "ok" : "err");
      if (res.ok) (e.target as HTMLFormElement).reset();
    } catch (error) {
      setStatus("err");
    } finally {
      setSending(false);
    }
  }

  return (
    <main>
      {/* Header */}
      <header
        className="sticky top-0 z-10 border-b backdrop-blur"
        style={{
          backgroundColor:
            "color-mix(in srgb, var(--surface) 80%, transparent)",
        }}
      >
        <div
          className="mx-auto flex max-w-6xl items-center justify-between px-4 py-3"
          style={{ color: "var(--foreground)" }}
        >
          <div className="text-xl font-extrabold tracking-tight">
            Raptor Finder
          </div>
          <a
            href="#lead"
            className="inline-flex items-center justify-center rounded-md px-6 py-3 font-semibold"
            style={{
              backgroundColor: "var(--primary)",
              color: "#fff",
            }}
            onMouseEnter={e =>
              (e.currentTarget.style.backgroundColor =
                "color-mix(in srgb, var(--primary) 85%, black)")
            }
            onMouseLeave={e =>
              (e.currentTarget.style.backgroundColor = "var(--primary)")
            }
          >
            <span className="hidden sm:inline">
              Search for a Raptor Near You
            </span>
            <span className="sm:hidden">Search</span>
          </a>
        </div>
      </header>

      {/* Hero */}
      <section className="relative">
        <div
          className="h-[44svh] bg-cover bg-center md:h-[60svh]"
          style={{ backgroundImage: "url('/hero-raptor.jpg')" }} // add an image in /public
          aria-hidden
        />
        <div className="absolute inset-0 bg-black/10" />
        <div className="absolute inset-0">
          <div className="mx-auto flex h-full max-w-6xl flex-col justify-center px-4 text-white">
            <h1 className="text-4xl font-extrabold md:text-6xl">
              Find Your Ford Raptor
            </h1>
            <p className="mt-4 max-w-2xl text-lg">
              We scan top marketplaces and dealers to help you score the right
              Raptor at the right price.
            </p>
            <a
              href="#lead"
              className="mt-8 inline-block w-fit rounded-md bg-white px-6 py-3 font-semibold text-black"
            >
              Start Your Search
            </a>
          </div>
        </div>
      </section>

      {/* Lead form + affiliate cards */}
      <section
        id="lead"
        className="mx-auto grid max-w-6xl gap-10 px-4 py-16 md:grid-cols-2"
      >
        {/* Lead form */}
        <div>
          <h2 className="text-2xl font-bold">Tell us what you’re after</h2>
          <p className="mt-2 text-zinc-600">
            We’ll match you with local deals and email a short list. No spam.
          </p>

          <form onSubmit={submitLead} className="mt-6 space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex flex-col gap-1">
                <label htmlFor="name" className="text-sm font-medium">
                  Name
                </label>
                <input
                  id="name"
                  name="name"
                  required
                  maxLength={100}
                  pattern="[A-Za-z\s]+"
                  title="Name should only contain letters and spaces"
                  className="h-11 rounded-md px-3"
                  style={{
                    backgroundColor: "var(--input-bg)",
                    color: "var(--foreground)",
                    border: "1px solid var(--input-border)",
                  }}
                  placeholder="Your name"
                />
              </div>
              <div className="flex flex-col gap-1">
                <label htmlFor="email" className="text-sm font-medium">
                  Email
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  className="h-11 rounded-md px-3"
                  style={{
                    backgroundColor: "var(--input-bg)",
                    color: "var(--foreground)",
                    border: "1px solid var(--input-border)",
                  }}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex flex-col gap-1">
                <label htmlFor="zip" className="text-sm font-medium">
                  ZIP code
                </label>
                <input
                  id="zip"
                  name="zip"
                  required
                  pattern="\d{5}"
                  title="5-digit ZIP"
                  className="h-11 rounded-md px-3"
                  style={{
                    backgroundColor: "var(--input-bg)",
                    color: "var(--foreground)",
                    border: "1px solid var(--input-border)",
                  }}
                  placeholder="73301"
                />
              </div>
              <div className="flex flex-col gap-1">
                <label htmlFor="budget" className="text-sm font-medium">
                  Budget
                </label>
                <input
                  id="budget"
                  name="budget"
                  className="h-11 rounded-md px-3"
                  style={{
                    backgroundColor: "var(--input-bg)",
                    color: "var(--foreground)",
                    border: "1px solid var(--input-border)",
                  }}
                  placeholder="$60k"
                />
              </div>
            </div>

            <div className="flex flex-col gap-1">
              <label htmlFor="trim" className="text-sm font-medium">
                Preferred Trim
              </label>
              <select
                id="trim"
                name="trim"
                className="h-11 rounded-md px-3"
                style={{
                  backgroundColor: "var(--input-bg)",
                  color: "var(--foreground)",
                  border: "1px solid var(--input-border)",
                }}
              >
                <option>Any</option>
                <option>Gen1</option>
                <option>Gen2</option>
                <option>Gen3</option>
                <option>37 Package</option>
                <option>R</option>
              </select>
            </div>

            <label className="mt-2 flex items-center gap-2 text-sm">
              <input type="checkbox" name="consent" required defaultChecked /> I
              agree to be contacted by trusted partners.
            </label>

            <button
              className="inline-flex items-center justify-center rounded-md px-6 py-3 font-semibold"
              style={{
                backgroundColor: "var(--primary)",
                color: "#fff",
              }}
              disabled={sending}
              onMouseEnter={e =>
                (e.currentTarget.style.backgroundColor =
                  "color-mix(in srgb, var(--primary) 85%, black)")
              }
              onMouseLeave={e =>
                (e.currentTarget.style.backgroundColor = "var(--primary)")
              }
            >
              {sending ? "Submitting..." : "Get Matches"}
            </button>

            {status === "ok" && (
              <p className="text-green-700">
                Thanks! We’ll be in touch shortly.
              </p>
            )}
            {status === "err" && (
              <p className="text-red-600">
                Something went wrong. Please try again.
              </p>
            )}
          </form>

          <p className="mt-3 text-xs text-zinc-500">
            RaptorFinder.com is not affiliated with Ford Motor Company.
          </p>
        </div>

        {/* Affiliate cards */}
        <div className="space-y-4">
          {listings.map(x => (
            <a
              key={x.id}
              href={x.href}
              target="_blank"
              rel="nofollow noopener"
              className="block overflow-hidden rounded-lg border border-zinc-200 hover:shadow"
            >
              <div
                className="h-48 bg-cover bg-center"
                style={{ backgroundImage: `url('${x.img}')` }}
                aria-label={x.title}
              />
              <div className="p-4">
                <div className="font-semibold">{x.title}</div>
                <div className="mt-1 text-sm text-zinc-600">
                  {x.price} • {x.miles}
                </div>
                <div className="mt-2 text-sm underline">
                  View on {x.source} →
                </div>
              </div>
            </a>
          ))}

          {/* Accessory picks (Amazon affiliate) */}
          <div className="rounded-lg border border-zinc-200 p-4">
            <div className="mb-2 font-semibold">Popular Accessories</div>
            <ul className="ml-5 list-disc text-sm">
              <li>
                <a
                  className="underline"
                  href="https://www.amazon.com/s?k=Ford+F150+All+Weather+Floor+Mats"
                  target="_blank"
                  rel="nofollow noopener"
                >
                  All‑weather floor mats
                </a>
              </li>
              <li>
                <a
                  className="underline"
                  href="https://www.amazon.com/s?k=ford+f150+bed+cover&crid=36GEN3KMQLRHJ&sprefix=Ford+F150+bed%2Caps%2C131&ref=nb_sb_ss_p13n-pd-dpltr-ranker_1_13"
                  target="_blank"
                  rel="nofollow noopener"
                >
                  Tri‑fold bed cover
                </a>
              </li>
              <li>
                <a
                  className="underline"
                  href="https://www.amazon.com/s?k=ford+f150+off+road+lights&crid=1AIXWJO5TTSV7&sprefix=ford+f150+off+road+li%2Caps%2C103&ref=nb_sb_ss_p13n-pd-dpltr-ranker_1_21"
                  target="_blank"
                  rel="nofollow noopener"
                >
                  Off‑road light kit
                </a>
              </li>
            </ul>
            <p className="mt-2 text-xs text-zinc-500">
              Some outbound links are affiliate links; we may earn a commission.
            </p>
          </div>
        </div>
      </section>

      {/* SEO content */}
      <section className="border-t">
        <div className="mx-auto max-w-6xl px-4 py-14">
          <h2 className="text-2xl font-bold">Why Ford Raptors hold value</h2>
          <p className="mt-3" style={{ color: "var(--foreground)" }}>
            The F‑150 Raptor blends Baja‑ready suspension, powerful drivetrains,
            and daily comfort. Gen2 (2017–2020) brought the 3.5L twin‑turbo V6;
            Gen3 (2021+) refined chassis and interior, added the 37 Package, and
            introduced the supercharged Raptor R.
          </p>
          <h3 className="mt-6 text-lg font-semibold">Typical price ranges</h3>
          <ul
            className="mt-2 list-disc pl-6"
            style={{ color: "var(--foreground)" }}
          >
            <li>Gen1 (2010–2014): $25k–$45k</li>
            <li>Gen2 (2017–2020): $45k–$65k</li>
            <li>Gen3 (2021+): $60k–$95k+ (R higher)</li>
          </ul>
        </div>
      </section>

      {/* Email signup */}
      <section className="mx-auto max-w-6xl px-4 pb-12">
        <div className="flex flex-col gap-4 rounded-xl border border-zinc-200 p-6 md:flex-row md:items-center">
          <div className="text-lg font-semibold">Get monthly Raptor deals</div>
          <form
            className="flex gap-2 md:ml-auto"
            action="https://formspree.io/f/xnnbqooa" // replace with Formspree/Mailchimp
            method="POST"
          >
            <input
              className="h-11 w-64 rounded-md px-3"
              style={{
                backgroundColor: "var(--input-bg)",
                color: "var(--foreground)",
                borderColor: "var(--input-border)",
                border: "1px solid",
              }}
              type="email"
              name="email"
              placeholder="<EMAIL>"
              required
            />
            <button
              className="inline-flex items-center justify-center rounded-md px-6 py-3 font-semibold"
              style={{
                backgroundColor: "var(--primary)",
                color: "#fff",
              }}
              onMouseEnter={e =>
                (e.currentTarget.style.backgroundColor =
                  "color-mix(in srgb, var(--primary) 85%, black)")
              }
              onMouseLeave={e =>
                (e.currentTarget.style.backgroundColor = "var(--primary)")
              }
            >
              Subscribe
            </button>
          </form>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t">
        <div className="mx-auto max-w-6xl px-4 py-10 text-sm text-zinc-600">
          <div><EMAIL></div>
          <div className="mt-2">
            © {new Date().getFullYear()} Raptor Finder. Not affiliated with
            Ford Motor Company.{" "}
            <a href="/privacy" className="underline">
              Privacy
            </a>{" "}
            ·{" "}
            <a href="/disclosure" className="underline">
              Affiliate Disclosure
            </a>{" "}
            ·{" "}
            <a href="/do-not-sell" className="underline">
              Do Not Sell or Share My Personal Information
            </a>
          </div>
        </div>
      </footer>
    </main>
  );
}
