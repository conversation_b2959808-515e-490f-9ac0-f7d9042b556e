import { Metadata } from "next";
import DoNotSellForm from "./DoNotSellForm";

export const metadata: Metadata = {
  title: "Do Not Sell or Share My Personal Information • Raptor Finder",
  description:
    "Submit a request to opt out of the sale or sharing of personal information, or request access/deletion.",
};

export default function DoNotSellPage() {
  return (
    <main className="mx-auto max-w-3xl px-4 py-12">
      <h1 className="text-3xl font-bold">
        Do Not Sell or Share My Personal Information
      </h1>
      <p className="mt-2 text-sm text-zinc-600">
        Effective date: August 12, 2025
      </p>

      <p className="mt-6">
        If you are a California resident (or where applicable in your region),
        you can use this page to:
      </p>
      <ul className="mt-3 list-disc pl-6">
        <li>Opt out of the sale or sharing of your personal information</li>
        <li>Request access to your information</li>
        <li>Request deletion of your information</li>
      </ul>

      <p className="mt-6">
        We use your information to match you with relevant listings and
        partners. If you opt out, we will honor your choice and will not share
        your information with third‑party partners for these purposes.
      </p>

      <DoNotSellForm />
    </main>
  );
}
    const form = e.currentTarget;
    const formData = new FormData(form);

    const email = String(formData.get("email") || "");
    const zip = String(formData.get("zip") || "");
    const notes = String(formData.get("notes") || "");

    // Validate inputs
    if (!validateEmail(email)) {
      e.preventDefault();
      alert("Please enter a valid email address");
      return;
    }

    if (zip && !validateZip(zip)) {
      e.preventDefault();
      alert("Please enter a valid ZIP code (e.g., 12345 or 12345-6789)");
      return;
    }

    // Sanitize inputs before submission
    formData.set("email", sanitizeInput(email));
    formData.set("zip", sanitizeInput(zip));
    formData.set("notes", sanitizeInput(notes));

    setIsSubmitting(true);
    // Form will submit normally to Formspree
  };
  return (
    <main className="mx-auto max-w-3xl px-4 py-12">
      <h1 className="text-3xl font-bold">
        Do Not Sell or Share My Personal Information
      </h1>
      <p className="mt-2 text-sm text-zinc-600">
        Effective date: August 12, 2025
      </p>

      <p className="mt-6">
        If you are a California resident (or where applicable in your region),
        you can use this page to:
      </p>
      <ul className="mt-3 list-disc pl-6">
        <li>Opt out of the sale or sharing of your personal information</li>
        <li>Request access to your information</li>
        <li>Request deletion of your information</li>
      </ul>

      <p className="mt-6">
        We use your information to match you with relevant listings and
        partners. If you opt out, we will honor your choice and will not share
        your information with third‑party partners for these purposes.
      </p>

      <form
        className="mt-8 space-y-4"
        action="https://formspree.io/f/xyzdyblj"
        method="POST"
        onSubmit={handleSubmit}
      >
        <div className="flex flex-col gap-1">
          <label htmlFor="email" className="text-sm font-medium">
            Email
          </label>
          <input
            id="email"
            name="email"
            type="email"
            required
            maxLength={100}
            pattern="[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}"
            title="Please enter a valid email address"
            className="h-11 rounded-md px-3"
            style={{
              backgroundColor: "var(--input-bg)",
              color: "var(--foreground)",
              borderColor: "var(--input-border)",
              border: "1px solid",
            }}
            placeholder="<EMAIL>"
          />
        </div>

        <div className="flex flex-col gap-1">
          <label htmlFor="zip" className="text-sm font-medium">
            ZIP code (optional)
          </label>
          <input
            id="zip"
            name="zip"
            type="text"
            maxLength={10}
            pattern="\d{5}(-\d{4})?"
            title="Please enter a valid ZIP code (e.g., 12345 or 12345-6789)"
            className="h-11 rounded-md px-3"
            style={{
              backgroundColor: "var(--input-bg)",
              color: "var(--foreground)",
              borderColor: "var(--input-border)",
              border: "1px solid",
            }}
            placeholder="73301"
          />
        </div>

        <div className="flex flex-col gap-1">
          <label htmlFor="requestType" className="text-sm font-medium">
            Request type
          </label>
          <select
            id="requestType"
            name="requestType"
            className="h-11 rounded-md px-3"
            style={{
              backgroundColor: "var(--input-bg)",
              color: "var(--foreground)",
              borderColor: "var(--input-border)",
              border: "1px solid",
            }}
          >
            <option value="optOut">
              Do not sell or share my personal information
            </option>
            <option value="requestAccess">
              Request access (what data we have)
            </option>
            <option value="requestToDelete">Request deletion</option>
          </select>
        </div>

        <div className="flex flex-col gap-1">
          <label htmlFor="notes" className="text-sm font-medium">
            Notes (optional)
          </label>
          <textarea
            id="notes"
            name="notes"
            rows={4}
            maxLength={1000}
            title="Additional notes about your request (optional)"
            className="rounded-md px-3 py-2"
            style={{
              backgroundColor: "var(--input-bg)",
              color: "var(--foreground)",
              borderColor: "var(--input-border)",
              border: "1px solid",
            }}
            placeholder="Anything we should know about your request"
          />
        </div>

        <button
          className="mt-2 inline-flex items-center justify-center rounded-md bg-black px-6 py-3 font-semibold text-white disabled:opacity-50"
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Submitting..." : "Submit request"}
        </button>

        <p className="text-xs text-zinc-500">
          We may contact you at the email provided to verify your identity. We
          aim to process requests within 45 days.
        </p>
      </form>
    </main>
  );
}
