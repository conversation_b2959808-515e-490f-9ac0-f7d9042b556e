"use client";

import Link from "next/link";

export default function LegalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {/* Top bar (matches homepage style) */}
      <header
        className="sticky top-0 z-10 border-b backdrop-blur"
        style={{
          backgroundColor:
            "color-mix(in srgb, var(--surface) 80%, transparent)",
        }}
      >
        <div
          className="mx-auto flex max-w-6xl items-center justify-between px-4 py-3"
          style={{ color: "var(--foreground)" }}
        >
          <div className="flex items-center gap-3">
            <Link
              href="/"
              className="inline-flex items-center justify-center rounded-md px-6 py-3 font-semibold"
              style={{
                backgroundColor: "var(--primary)",
                color: "#fff",
              }}
              onMouseEnter={e =>
                (e.currentTarget.style.backgroundColor =
                  "color-mix(in srgb, var(--primary) 85%, black)")
              }
              onMouseLeave={e =>
                (e.currentTarget.style.backgroundColor = "var(--primary)")
              }
            >
              Home
            </Link>
            <div className="text-xl font-extrabold tracking-tight">
              Raptor Finder
            </div>
          </div>
          <nav className="hidden gap-4 text-sm sm:flex">
            <Link className="underline hover:no-underline" href="/privacy">
              Privacy
            </Link>
            <Link className="underline hover:no-underline" href="/disclosure">
              Affiliate Disclosure
            </Link>
            <Link className="underline hover:no-underline" href="/do-not-sell">
              Do Not Sell
            </Link>
          </nav>
        </div>
      </header>

      {/* Page content */}
      {children}

      {/* Footer (matches homepage spacing/tones) */}
      <footer className="border-t">
        <div className="mx-auto max-w-6xl px-4 py-10 text-sm text-zinc-600">
          <div><EMAIL></div>
          <div className="mt-2">
            © {new Date().getFullYear()} Raptor Finder.{" "}
            <Link className="underline" href="/privacy">
              Privacy
            </Link>{" "}
            ·{" "}
            <Link className="underline" href="/disclosure">
              Affiliate Disclosure
            </Link>{" "}
            ·{" "}
            <Link className="underline" href="/do-not-sell">
              Do Not Sell or Share
            </Link>
          </div>
        </div>
      </footer>
    </>
  );
}
