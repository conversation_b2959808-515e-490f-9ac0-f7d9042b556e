import type { Metadata } from "next";
import "./globals.css";

export const metadata: Metadata = {
  title: "Raptor Finder - Find Your Ford Raptor Fast",
  description:
    "We scan top marketplaces and dealers to help you score the right Ford Raptor at the right price. Find F-150 Raptors, Ranger Raptors, and Bronco Raptors from trusted dealers.",
  keywords:
    "Ford Raptor, F-150 Raptor, Ranger Raptor, Bronco Raptor, truck finder, Ford dealer, used trucks",
  openGraph: {
    title: "Raptor Finder - Find Your Ford Raptor Fast",
    description:
      "We scan top marketplaces and dealers to help you score the right Ford Raptor at the right price.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Raptor Finder - Find Your Ford Raptor Fast",
    description:
      "We scan top marketplaces and dealers to help you score the right Ford Raptor at the right price.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className="antialiased">{children}</body>
    </html>
  );
}
